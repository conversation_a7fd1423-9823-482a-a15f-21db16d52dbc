"use client";

import {  useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import {
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON><PERSON>,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import {
  Plus,
  Gift,
  Sparkles,
  Tag,
  Pencil,
  PlusCircle,
  Search,
  ArrowUpDown,
  Loader2,
  ArrowUpCircle,
} from "lucide-react";
import { useAuth } from "@/hooks/use-auth";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
// Removed unused 'Tabs', 'TabsContent', 'TabsList', and 'TabsTrigger'

interface Product {
  id: string;
  title: string;
  description: string | null;
  status: "active" | "inactive";
  is_ai_powered: boolean;
  has_free_tier: boolean;
  keywords: string[];
  view_count: number;
  clicks: number;
  conversions: number;
  created_at: number;
  category?: string;
}

export default function ProductsPage() {
  const { user } = useAuth();
  const router = useRouter();
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [sortBy, setSortBy] = useState("newest");
  const [hasActiveOffer, setHasActiveOffer] = useState(false);
  const canAddMoreProducts = true;

  // Fetch products
  useEffect(() => {
    if (!user) return;

    const fetchProducts = async () => {
      setLoading(true);
      try {
        const token = await user.getIdToken();

        const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/products/brand/all`, {
          method: "GET",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (!res.ok) {
          throw new Error("Failed to fetch products");
        }

        const data = await res.json();
        setProducts(data.products || []);
      } catch (err) {
        console.error("❌ Error fetching products from API:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [user]);

  // Check if user has active offers
  useEffect(() => {
    if (!user) return;

    const checkActiveOffers = async () => {
      try {
        const token = await user.getIdToken();
        const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/offers/brand/all`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (res.ok) {
          const data = await res.json();
          const activeOffers = data.offers.filter((offer: { active: boolean }) => offer.active);
          setHasActiveOffer(activeOffers.length > 0);
        }
      } catch (err) {
        console.error("Error checking active offers:", err);
      }
    };

    checkActiveOffers();
  }, [user]);

  // Get unique categories from products
  const categories = ["all", ...new Set(products.map(p => p.category).filter(Boolean))];

  // Filter and sort products
  const filteredProducts = products
    .filter(product => {
      const matchesSearch = product.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           (product.description && product.description.toLowerCase().includes(searchQuery.toLowerCase()));
      const matchesCategory = categoryFilter === "all" || product.category === categoryFilter;
      return matchesSearch && matchesCategory;
    })
    .sort((a, b) => {
      if (sortBy === "newest") {
        return (new Date(b.created_at).getTime()) - (new Date(a.created_at).getTime());
      } else if (sortBy === "oldest") {
        return (new Date(a.created_at).getTime()) - (new Date(b.created_at).getTime());
      } else if (sortBy === "alphabetical") {
        return a.title.localeCompare(b.title);
      }
      return 0;
    });

  return (
    <div className="min-h-screen bg-gray-50/30">
      {/* Header Section */}
      <div className="bg-white border-b border-gray-200">
        <div className="px-6 py-8">
          <div className="max-w-7xl mx-auto">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 tracking-tight">
                  Your Products
                </h1>
                <p className="mt-2 text-sm text-gray-600">
                  Manage all tools you&apos;ve submitted to AdMesh and track their performance
                </p>
                {!loading && (
                  <div className="mt-2 text-sm text-gray-500">
                    {products.length} {products.length === 1 ? 'product' : 'products'} in total
                  </div>
                )}
              </div>

              <div className="flex flex-col sm:flex-row gap-3">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div>
                        <Button
                          onClick={() => router.push("/dashboard/brand/products/new")}
                          className="bg-gray-900 hover:bg-gray-800 text-white font-medium px-6 py-2 rounded-lg transition-all duration-200 flex items-center gap-2"
                          disabled={!canAddMoreProducts}
                        >
                          <Plus className="w-5 h-5" />
                          Add Product
                        </Button>
                      </div>
                    </TooltipTrigger>
                    {!canAddMoreProducts && (
                      <TooltipContent>
                        <p>You&apos;ve reached your product limit. <br />Upgrade your subscription to add more products.</p>
                      </TooltipContent>
                    )}
                  </Tooltip>
                </TooltipProvider>
                <Button
                  variant="outline"
                  onClick={() => setShowFetchDialog(true)}
                  className="border-gray-200 hover:border-gray-300 hover:bg-gray-50 font-medium px-6 py-2 rounded-lg transition-all duration-200 flex items-center gap-2"
                >
                  <RefreshCw className="h-4 w-4" />
                  Fetch from Website
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="px-6 py-8">
        <div className="max-w-7xl mx-auto space-y-8">

          {/* Search and Filter Section */}
          <div className="bg-white rounded-xl border border-gray-200 p-6 shadow-sm">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="md:col-span-2">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search products..."
                    className="pl-10 w-full border-gray-200 focus:border-gray-400 focus:ring-gray-400"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>
              <div className="flex gap-3">
                <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                  <SelectTrigger className="w-full border-gray-200 focus:border-gray-400 focus:ring-gray-400">
                    <SelectValue placeholder="Category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map(category => (
                      <SelectItem key={category} value={category || ""}>
                        {category === "all" ? "All Categories" : category || "Uncategorized"}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="shrink-0 border-gray-200 hover:border-gray-300 hover:bg-gray-50">
                      <ArrowUpDown className="h-4 w-4 mr-2" />
                      Sort
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => setSortBy("newest")}>
                      Newest First
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setSortBy("oldest")}>
                      Oldest First
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setSortBy("alphabetical")}>
                      Alphabetical
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </div>

      {loading ? (
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : filteredProducts.length === 0 ? (
        <div className="bg-muted/40 border rounded-lg py-16 text-center">
          <div className="max-w-sm mx-auto">
            <h3 className="text-lg font-medium">No products found</h3>
            <p className="text-muted-foreground mt-2">
              {products.length === 0
                ? "Get started by adding your first product to AdMesh."
                : "Try adjusting your search or filters."}
            </p>
            {products.length === 0 && (
              canAddMoreProducts ? (
                <Button onClick={() => router.push("/dashboard/brand/products/new")} className="mt-4">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Your First Product
                </Button>
              ) : (
                <Button onClick={() => router.push("/dashboard/brand/subscription")} className="mt-4">
                  <ArrowUpCircle className="w-4 h-4 mr-2" />
                  Upgrade to Add Products
                </Button>
              )
            )}
          </div>
        </div>
      ) : (
          {/* Products Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredProducts.map((product) => (
              <Card key={product.id} className="overflow-hidden bg-white border border-gray-200 hover:border-gray-300 hover:shadow-lg transition-all duration-200 group">
                <CardHeader className="pb-4">
                  <div className="flex items-start justify-between">
                    <CardTitle className="text-lg font-semibold text-gray-900 line-clamp-1 group-hover:text-gray-700 transition-colors">
                      {product.title}
                    </CardTitle>
                    <Badge
                      variant={product.status === "active" ? "default" : "secondary"}
                      className={`text-xs ${
                        product.status === "active"
                          ? "bg-green-100 text-green-800 border-green-200"
                          : "bg-gray-100 text-gray-600 border-gray-200"
                      }`}
                    >
                      {product.status === "active" ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">
                    {product.category || "Uncategorized"}
                  </p>
                </CardHeader>

                <CardContent className="pb-4">
                  {product.description && (
                    <p className="text-sm text-gray-600 line-clamp-2 h-10 mb-4">
                      {product.description}
                    </p>
                  )}

                  <div className="flex flex-wrap gap-2">
                    {product.is_ai_powered && (
                      <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200 hover:bg-purple-100 transition-colors">
                        <Sparkles className="h-3 w-3 mr-1" />
                        AI Powered
                      </Badge>
                    )}

                    {product.has_free_tier && (
                      <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 hover:bg-green-100 transition-colors">
                        <Gift className="h-3 w-3 mr-1" />
                        Free Tier
                      </Badge>
                    )}

                    {product.keywords?.slice(0, 2).map((keyword, i) => (
                      <Badge key={i} variant="outline" className="text-gray-600 border-gray-200 hover:bg-gray-50 transition-colors">
                        <Tag className="h-3 w-3 mr-1" />
                        {keyword}
                      </Badge>
                    ))}

                    {product.keywords?.length > 2 && (
                      <Badge variant="outline" className="text-gray-600 border-gray-200 hover:bg-gray-50 transition-colors">
                        +{product.keywords.length - 2} more
                      </Badge>
                    )}
                  </div>
                </CardContent>

                <CardFooter className="border-t border-gray-100 bg-gray-50/50 pt-4">
                  <div className="flex gap-3 w-full">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1 border-gray-200 hover:border-gray-300 hover:bg-white transition-all duration-200"
                      onClick={() => router.push(`/dashboard/brand/products/${product.id}/edit`)}
                    >
                      <Pencil className="h-4 w-4 mr-1" />
                      Edit
                    </Button>

                    <Button
                      size="sm"
                      className="flex-1 bg-gray-900 hover:bg-gray-800 text-white transition-all duration-200"
                      onClick={() => {
                        if (hasActiveOffer) {
                          toast.error("You can only have one active offer at a time. Please deactivate your existing offer before creating a new one.");
                        } else {
                          router.push(`/dashboard/brand/offers/new?productId=${product.id}`);
                        }
                      }}
                    >
                      <PlusCircle className="h-4 w-4 mr-1" />
                      Create Offer
                    </Button>
                  </div>
                </CardFooter>
              </Card>
            ))}
          </div>
      )}

          {/* AdMesh Branding Footer */}
          <div className="mt-12 pt-8 border-t border-gray-200">
            <div className="flex items-center justify-center">
              <div className="flex items-center gap-2 text-sm text-gray-500">
                <span>Powered by</span>
                <span className="font-semibold text-gray-900">AdMesh</span>
                <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full font-medium">BETA</span>
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>
  );
}